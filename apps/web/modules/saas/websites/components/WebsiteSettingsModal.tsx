"use client";

import { Bad<PERSON> } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import { ExternalLinkIcon, GlobeIcon, SaveIcon } from "lucide-react";
import { useState } from "react";
import { WebsiteService } from "../lib/website-service";
import {
	type GeneratedWebsite,
	WebsiteFeature,
} from "../utils/website-generator";

interface WebsiteSettingsModalProps {
	website: GeneratedWebsite;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onWebsiteUpdate: (website: GeneratedWebsite) => void;
}

export function WebsiteSettingsModal({
	website,
	open,
	onOpenChange,
	onWebsiteUpdate,
}: WebsiteSettingsModalProps) {
	const [saving, setSaving] = useState(false);
	const [config, setConfig] = useState(website.config);

	const handleSave = async () => {
		setSaving(true);
		try {
			const updatedWebsite = await WebsiteService.updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				onWebsiteUpdate(updatedWebsite);
				onOpenChange(false);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	const toggleFeature = (feature: WebsiteFeature) => {
		setConfig((prev) => ({
			...prev,
			features: prev.features.includes(feature)
				? prev.features.filter((f) => f !== feature)
				: [...prev.features, feature],
		}));
	};

	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-w-5xl w-full h-[85vh] flex flex-col p-0 gap-0">
				{/* Fixed Header */}
				<div className="flex items-center justify-between p-6 border-b border-border">
					<div>
						<DialogTitle className="text-lg font-semibold">
							Project Settings
						</DialogTitle>
						<DialogDescription className="text-sm text-muted-foreground mt-1">
							Configure your website deployment and features
						</DialogDescription>
					</div>
					<div className="flex items-center space-x-2">
						<Button
							variant="outline"
							onClick={() => onOpenChange(false)}
						>
							Cancel
						</Button>
						<Button onClick={handleSave} disabled={saving}>
							{saving ? (
								<>
									<SaveIcon className="h-4 w-4 mr-2 animate-spin" />
									Saving...
								</>
							) : (
								<>
									<SaveIcon className="h-4 w-4 mr-2" />
									Save Changes
								</>
							)}
						</Button>
					</div>
				</div>

				{/* Fixed Tabs Navigation */}
				<Tabs
					defaultValue="general"
					className="flex flex-col flex-1 min-h-0"
				>
					<div className="px-6 pt-4 border-b border-border">
						<TabsList className="grid w-full grid-cols-4 bg-muted/30">
							<TabsTrigger value="general" className="text-sm">
								General
							</TabsTrigger>
							<TabsTrigger value="design" className="text-sm">
								Design
							</TabsTrigger>
							<TabsTrigger value="features" className="text-sm">
								Features
							</TabsTrigger>
							<TabsTrigger value="preview" className="text-sm">
								Preview
							</TabsTrigger>
						</TabsList>
					</div>

					{/* Fixed Content Area with Scroll */}
					<div className="flex-1 overflow-y-auto">
						<div className="p-6 min-h-full">
							{/* General Tab */}
							<TabsContent
								value="general"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-4">
											Project Information
										</h3>
										<div className="grid grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label
													htmlFor="projectName"
													className="text-sm font-medium"
												>
													Project Name
												</Label>
												<Input
													id="projectName"
													value={templateInfo.name}
													disabled
													className="bg-muted/50"
												/>
											</div>
											<div className="space-y-2">
												<Label
													htmlFor="template"
													className="text-sm font-medium"
												>
													Template
												</Label>
												<Input
													id="template"
													value={templateInfo.name}
													disabled
													className="bg-muted/50"
												/>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Domain Configuration
										</h3>
										<div className="space-y-4">
											<div className="p-4 bg-muted/30 rounded-lg border">
												<div className="flex items-center justify-between">
													<div>
														<p className="font-medium text-sm">
															Production Domain
														</p>
														<p className="text-sm text-muted-foreground font-mono">
															{website.url
																.replace(
																	"https://",
																	"",
																)
																.replace(
																	"http://",
																	"",
																)}
														</p>
													</div>
													<Badge status="success">
														Active
													</Badge>
												</div>
											</div>
										</div>
										<div className="grid grid-cols-2 gap-4 mt-4">
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													SSL Certificate
												</p>
												<p className="text-sm font-medium text-green-600">
													Valid
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													CDN Status
												</p>
												<p className="text-sm font-medium text-green-600">
													Active
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Deployment Information
										</h3>
										<div className="grid grid-cols-3 gap-4">
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													Status
												</p>
												<p className="text-sm font-medium capitalize">
													{website.status}
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													Last Deploy
												</p>
												<p className="text-sm font-medium">
													{new Date(
														website.updatedAt,
													).toLocaleDateString(
														"en-US",
														{
															month: "short",
															day: "numeric",
														},
													)}
												</p>
											</div>
											<div className="p-3 border rounded-lg">
												<p className="text-xs text-muted-foreground uppercase tracking-wide mb-1">
													Build Time
												</p>
												<p className="text-sm font-medium">
													~2m 15s
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Environment
										</h3>
										<div className="p-4 bg-muted/30 rounded-lg border">
											<div className="flex items-center justify-between">
												<div>
													<p className="text-sm font-medium">
														Production Environment
													</p>
													<p className="text-xs text-muted-foreground">
														3 variables configured
													</p>
												</div>
												<Button
													variant="outline"
													size="sm"
												>
													Manage
												</Button>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Design Tab */}
							<TabsContent
								value="design"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-4">
											Template
										</h3>
										<div className="p-4 border rounded-lg bg-card">
											<div className="flex items-center gap-4">
												<div
													className="w-12 h-12 rounded-lg"
													style={{
														backgroundColor:
															templateInfo.color,
													}}
												/>
												<div className="flex-1">
													<h4 className="font-medium">
														{templateInfo.name}
													</h4>
													<p className="text-sm text-muted-foreground">
														{
															templateInfo.description
														}
													</p>
												</div>
												<Badge status="success">
													Active
												</Badge>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Customization
										</h3>
										<div className="grid grid-cols-2 gap-4">
											<div className="space-y-2">
												<Label
													htmlFor="primaryColor"
													className="text-sm font-medium"
												>
													Primary Color
												</Label>
												<Input
													id="primaryColor"
													type="color"
													value={config.primaryColor}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															primaryColor:
																e.target.value,
														}))
													}
												/>
											</div>
											<div className="space-y-2">
												<Label
													htmlFor="secondaryColor"
													className="text-sm font-medium"
												>
													Secondary Color
												</Label>
												<Input
													id="secondaryColor"
													type="color"
													value={
														config.secondaryColor
													}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															secondaryColor:
																e.target.value,
														}))
													}
												/>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Typography
										</h3>
										<div className="space-y-4">
											<div className="space-y-2">
												<Label
													htmlFor="fontFamily"
													className="text-sm font-medium"
												>
													Font Family
												</Label>
												<select
													id="fontFamily"
													value={config.fontFamily}
													onChange={(e) =>
														setConfig((prev) => ({
															...prev,
															fontFamily:
																e.target.value,
														}))
													}
													className="w-full p-2 border border-border rounded-md bg-background"
												>
													<option value="Inter">
														Inter
													</option>
													<option value="Roboto">
														Roboto
													</option>
													<option value="Open Sans">
														Open Sans
													</option>
													<option value="Lato">
														Lato
													</option>
												</select>
											</div>
											<div className="p-3 bg-muted/30 rounded-lg border">
												<p className="text-sm text-muted-foreground mb-2">
													Preview
												</p>
												<p
													className="font-medium"
													style={{
														fontFamily:
															config.fontFamily,
													}}
												>
													The quick brown fox jumps
													over the lazy dog
												</p>
											</div>
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Layout Options
										</h3>
										<div className="grid grid-cols-3 gap-3">
											{[
												"modern",
												"classic",
												"minimal",
											].map((layout) => (
												<div
													key={layout}
													className={`p-3 border rounded-lg cursor-pointer transition-colors ${
														config.layout === layout
															? "border-primary bg-primary/5"
															: "border-border hover:border-primary/50"
													}`}
													onClick={() =>
														setConfig((prev) => ({
															...prev,
															layout: layout as any,
														}))
													}
												>
													<div className="text-center">
														<div
															className={`w-full h-12 rounded mb-2 ${
																layout ===
																"modern"
																	? "bg-gradient-to-r from-blue-500 to-purple-500"
																	: layout ===
																			"classic"
																		? "bg-gradient-to-r from-amber-500 to-orange-500"
																		: "bg-gradient-to-r from-gray-400 to-gray-600"
															}`}
														/>
														<p className="text-xs font-medium capitalize">
															{layout}
														</p>
													</div>
												</div>
											))}
										</div>
									</div>

									<div>
										<h3 className="text-base font-medium mb-4">
											Color Palette
										</h3>
										<div className="grid grid-cols-5 gap-3">
											{[
												{
													name: "Primary",
													color: config.primaryColor,
												},
												{
													name: "Secondary",
													color: config.secondaryColor,
												},
												{
													name: "Background",
													color: "#ffffff",
												},
												{
													name: "Text",
													color: "#000000",
												},
												{
													name: "Accent",
													color: templateInfo.color,
												},
											].map((item) => (
												<div
													key={item.name}
													className="text-center"
												>
													<div
														className="w-full h-12 rounded-lg border border-border mb-2"
														style={{
															backgroundColor:
																item.color,
														}}
													/>
													<p className="text-xs text-muted-foreground">
														{item.name}
													</p>
												</div>
											))}
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Features Tab */}
							<TabsContent
								value="features"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-4">
											Website Features
										</h3>
										<div className="space-y-4">
											{Object.values(WebsiteFeature).map(
												(feature) => (
													<div
														key={feature}
														className="flex items-center justify-between p-4 border rounded-lg"
													>
														<div>
															<p className="font-medium capitalize">
																{feature
																	.replace(
																		/([A-Z])/g,
																		" $1",
																	)
																	.trim()}
															</p>
															<p className="text-sm text-muted-foreground">
																{feature ===
																	WebsiteFeature.CONTACT_FORM &&
																	"Allow visitors to contact you"}
																{feature ===
																	WebsiteFeature.ONLINE_BOOKING &&
																	"Enable online appointment booking"}
																{feature ===
																	WebsiteFeature.GALLERY &&
																	"Showcase your work with image gallery"}
																{feature ===
																	WebsiteFeature.MENU &&
																	"Display your menu or services"}
																{feature ===
																	WebsiteFeature.TESTIMONIALS &&
																	"Show customer reviews and testimonials"}
																{feature ===
																	WebsiteFeature.LOCATION_MAP &&
																	"Display your business location"}
																{feature ===
																	WebsiteFeature.SOCIAL_LINKS &&
																	"Connect your social media accounts"}
																{feature ===
																	WebsiteFeature.BUSINESS_HOURS &&
																	"Show your operating hours"}
																{feature ===
																	WebsiteFeature.SERVICES_LIST &&
																	"List your services or products"}
																{feature ===
																	WebsiteFeature.TEAM_SECTION &&
																	"Introduce your team members"}
															</p>
														</div>
														<Switch
															checked={config.features.includes(
																feature,
															)}
															onCheckedChange={() =>
																toggleFeature(
																	feature,
																)
															}
														/>
													</div>
												),
											)}
										</div>
									</div>
								</div>
							</TabsContent>

							{/* Preview Tab */}
							<TabsContent
								value="preview"
								className="space-y-6 mt-0"
							>
								<div className="space-y-6">
									<div>
										<h3 className="text-base font-medium mb-4">
											Website Preview
										</h3>
										<div className="border rounded-lg bg-muted/30 p-4">
											<div className="flex items-center justify-between mb-4">
												<p className="text-sm text-muted-foreground">
													Preview your website changes
												</p>
												<Button size="sm" asChild>
													<a
														href={website.url}
														target="_blank"
														rel="noopener noreferrer"
													>
														<ExternalLinkIcon className="h-3 w-3 mr-1" />
														Open in New Tab
													</a>
												</Button>
											</div>
											<div className="aspect-video bg-background border rounded-md flex items-center justify-center">
												<div className="text-center">
													<GlobeIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
													<p className="text-sm text-muted-foreground">
														Website preview will
														appear here
													</p>
												</div>
											</div>
										</div>
									</div>
								</div>
							</TabsContent>
						</div>
					</div>
				</Tabs>
			</DialogContent>
		</Dialog>
	);
}
