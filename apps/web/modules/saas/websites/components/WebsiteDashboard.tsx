"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Button } from "@ui/components/button";
import {} from "@ui/components/card";
import {
	AlertCircleIcon,
	ExternalLinkIcon,
	GlobeIcon,
	LoaderIcon,
	SettingsIcon,
} from "lucide-react";
import { useEffect, useState } from "react";
import { WebsiteService } from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";
import { WebsiteAnalyticsCharts } from "./WebsiteAnalyticsCharts";
import { WebsiteSettingsModal } from "./WebsiteSettingsModal";

export function WebsiteDashboard() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [settingsOpen, setSettingsOpen] = useState(false);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) return;

		try {
			setLoading(true);
			const websiteData = await WebsiteService.getWebsiteByOrganization(
				activeOrganization.id,
			);
			setWebsite(websiteData);
			setError(null);
		} catch (err) {
			setError("Failed to load website data");
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<LoaderIcon className="size-5 animate-spin" />
					<span className="text-muted-foreground">
						Loading website data...
					</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-destructive">
					<AlertCircleIcon className="size-5" />
					<span>{error}</span>
				</div>
			</div>
		);
	}

	if (!website) {
		return (
			<div className="text-center py-12">
				<div className="max-w-md mx-auto">
					<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
					<h3 className="text-lg font-semibold mb-2">
						No Website Found
					</h3>
					<p className="text-muted-foreground mb-4">
						It looks like your website hasn't been generated yet.
						This usually happens automatically during onboarding.
					</p>
					<Button onClick={loadWebsite}>Refresh</Button>
				</div>
			</div>
		);
	}

	const { config, status, url } = website;
	const templateInfo = WebsiteService.getTemplateInfo(config.template);

	return (
		<div className="space-y-6">
			{/* Vercel-style Project Overview */}
			<div className="border border-border rounded-lg bg-card">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-border">
					<div className="flex items-center space-x-3">
						<div className="w-8 h-8 bg-black dark:bg-white rounded-md flex items-center justify-center">
							<GlobeIcon className="h-4 w-4 text-white dark:text-black" />
						</div>
						<div>
							<h1 className="font-semibold text-lg">
								{templateInfo.name}
							</h1>
							<p className="text-sm text-muted-foreground">
								{templateInfo.description}
							</p>
						</div>
					</div>
					<div className="flex items-center space-x-3">
						<div className="flex items-center space-x-2">
							<div
								className={`w-2 h-2 rounded-full ${
									status === "active"
										? "bg-green-500"
										: status === "generating"
											? "bg-yellow-500"
											: "bg-red-500"
								}`}
							/>
							<span className="text-sm font-medium capitalize">
								{status}
							</span>
						</div>
						<Button size="sm" asChild>
							<a
								href={url}
								target="_blank"
								rel="noopener noreferrer"
							>
								<ExternalLinkIcon className="h-3 w-3 mr-1" />
								Visit
							</a>
						</Button>
					</div>
				</div>

				{/* Production Deployment */}
				<div className="p-6 border-b border-border">
					<div className="flex items-center justify-between mb-4">
						<h2 className="font-medium">Production Deployment</h2>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setSettingsOpen(true)}
						>
							<SettingsIcon className="h-3 w-3 mr-1" />
							Settings
						</Button>
					</div>
					<div className="bg-muted/30 rounded-md p-4">
						<div className="flex items-center justify-between">
							<div className="flex items-center space-x-3">
								<div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
									<div className="w-2 h-2 bg-green-600 rounded-full" />
								</div>
								<div>
									<p className="font-mono text-sm">
										{url
											.replace("https://", "")
											.replace("http://", "")}
									</p>
									<p className="text-xs text-muted-foreground">
										{new Date(
											website.updatedAt,
										).toLocaleDateString("en-US", {
											month: "short",
											day: "numeric",
											hour: "2-digit",
											minute: "2-digit",
										})}{" "}
										• {templateInfo.name} Template
									</p>
								</div>
							</div>
							<div className="flex items-center space-x-2">
								<Button variant="ghost" size="sm" asChild>
									<a
										href={url}
										target="_blank"
										rel="noopener noreferrer"
									>
										<ExternalLinkIcon className="h-3 w-3" />
									</a>
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Quick Stats */}
				<div className="p-6">
					<h2 className="font-medium mb-4">Overview</h2>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
						<div className="space-y-1">
							<p className="text-xs text-muted-foreground uppercase tracking-wide">
								Status
							</p>
							<p className="font-semibold capitalize">{status}</p>
						</div>
						<div className="space-y-1">
							<p className="text-xs text-muted-foreground uppercase tracking-wide">
								Template
							</p>
							<p className="font-semibold">{templateInfo.name}</p>
						</div>
						<div className="space-y-1">
							<p className="text-xs text-muted-foreground uppercase tracking-wide">
								Last Deploy
							</p>
							<p className="font-semibold">
								{Math.floor(
									(Date.now() -
										new Date(website.updatedAt).getTime()) /
										(1000 * 60 * 60 * 24),
								)}
								d ago
							</p>
						</div>
						<div className="space-y-1">
							<p className="text-xs text-muted-foreground uppercase tracking-wide">
								Domain
							</p>
							<p className="font-semibold font-mono text-sm">
								Custom
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Enhanced Analytics Charts */}
			<WebsiteAnalyticsCharts website={website} />

			{/* Settings Modal */}
			{website && (
				<WebsiteSettingsModal
					website={website}
					open={settingsOpen}
					onOpenChange={setSettingsOpen}
					onWebsiteUpdate={setWebsite}
				/>
			)}
		</div>
	);
}
